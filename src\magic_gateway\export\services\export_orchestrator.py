"""
Export orchestration service for coordinating the complete export workflow.

This module provides the ExportOrchestrator service that manages the complete
export workflow including metadata analysis, optimization strategy selection,
and export execution with streaming response generation.
"""

import uuid
from pathlib import Path
from typing import Optional, Any, Union, Dict, AsyncGenerator

from fastapi import HTTPException
from fastapi.responses import StreamingResponse

from magic_gateway.core.logging_config import log
from magic_gateway.db.connection_manager import ClickHouseConnectionManager
from magic_gateway.export.exceptions import (
    ExportError,
    ExportErrorContext,
    ExcelLimitExceededError,
    MetadataRetrievalError,
)
from magic_gateway.export.models import (
    ExportFormat,
    ExportOptions,
    JobMetadata,
    OptimizationStrategy,
    PeriodSeparation,
)
from magic_gateway.export.services.metadata_analyzer import MetadataAnalyzer
from magic_gateway.export.formats.converter import FormatConverter
from magic_gateway.utils.temp_file_manager import temp_file_manager, FileCategory


class ExportOrchestrator:
    """
    Main coordination service for export operations.

    This class orchestrates the complete export workflow by coordinating
    metadata analysis, optimization strategy selection, and export execution.
    It provides the main entry point for export operations and handles
    error management and resource cleanup.
    """

    def __init__(self, connection_manager: ClickHouseConnectionManager):
        """
        Initialize the export orchestrator.

        Args:
            connection_manager: ClickHouse connection manager for database operations
        """
        self.connection_manager = connection_manager
        self.metadata_analyzer = MetadataAnalyzer(connection_manager)
        self.format_converter = FormatConverter(connection_manager)
        self.cluster_name = getattr(connection_manager, "cluster_name", "primary")

    async def export_job_data(
        self,
        job_id: int,
        format: ExportFormat,
        options: ExportOptions,
        request_id: Optional[str] = None,
        job_info: Optional[Any] = None,
        return_download_url: bool = False,
        request: Optional[Any] = None,
    ) -> Union[StreamingResponse, Dict[str, Any]]:
        """
        Main orchestration method for exporting job data.

        This method coordinates the complete export workflow:
        1. Analyze job metadata to understand dataset characteristics
        2. Select optimization strategy based on metadata and format
        3. Execute export using the selected strategy
        4. Return streaming response with the exported data

        Args:
            job_id: Job ID to export data for
            format: Target export format (CSV, Excel, Parquet)
            options: Export options including layout and period separation
            request_id: Optional request ID for tracking and error context
            job_info: Optional job information for Excel info sheet
            return_download_url: Whether to return download URL instead of streaming response
            request: Optional FastAPI request object for URL generation

        Returns:
            StreamingResponse containing the exported data, or Dict with download URL if return_download_url=True

        Raises:
            HTTPException: For client errors (400-level)
            ExportError: For server errors (500-level)
        """
        request_id = request_id or str(uuid.uuid4())

        with ExportErrorContext(request_id, "export_orchestration", job_id) as ctx:
            ctx.add_context("format", format.value)
            ctx.add_context(
                "options",
                {
                    "separate_periods": options.separate_periods,
                    "horizontal_facts": options.horizontal_facts,
                    "excel_layout": options.excel_layout.value
                    if options.excel_layout
                    else None,
                    "compression": options.compression,
                },
            )

            try:
                log.info(
                    f"Starting export orchestration for job {job_id} in {format.value} format (request {request_id})"
                )

                # Step 1: Analyze metadata
                log.debug(f"Step 1: Analyzing metadata for job {job_id}")
                metadata = await self._analyze_metadata(job_id, request_id)
                ctx.add_context(
                    "metadata",
                    {
                        "total_rows": metadata.total_rows,
                        "periods_count": len(metadata.available_periods),
                        "facts_count": len(metadata.facts_list),
                        "table_name": metadata.table_name,
                    },
                )

                # Step 2: Select optimization strategy
                log.debug(f"Step 2: Selecting optimization strategy for job {job_id}")
                strategy = await self._select_optimization_strategy(
                    metadata, format, options, request_id
                )
                ctx.add_context(
                    "strategy",
                    {
                        "use_streaming": strategy.use_streaming,
                        "chunk_size": strategy.chunk_size,
                        "period_chunking": strategy.period_chunking,
                        "memory_limit_mb": strategy.memory_limit_mb,
                        "temp_file_strategy": strategy.temp_file_strategy.value,
                    },
                )

                # Step 3: Execute export
                log.debug(f"Step 3: Executing export for job {job_id}")
                response = await self._execute_export(
                    metadata=metadata,
                    strategy=strategy,
                    format=format,
                    options=options,
                    request_id=request_id,
                    job_info=job_info,
                    return_download_url=return_download_url,
                    request=request,
                )

                log.info(
                    f"Successfully completed export orchestration for job {job_id} (request {request_id})"
                )
                return response

            except ExcelLimitExceededError as e:
                # Convert Excel limit errors to client errors
                log.warning(f"Excel limit exceeded for job {job_id}: {e.message}")
                raise HTTPException(
                    status_code=400,
                    detail={
                        "error": "excel_limit_exceeded",
                        "message": e.message,
                        "total_rows": e.total_rows,
                        "excel_limit": e.excel_limit,
                        "suggestions": e.recovery_suggestions,
                        "request_id": request_id,
                    },
                )
            except MetadataRetrievalError as e:
                # Convert metadata errors to client errors
                log.error(f"Metadata retrieval failed for job {job_id}: {e.message}")
                raise HTTPException(
                    status_code=404,
                    detail={
                        "error": "job_not_found",
                        "message": f"Job {job_id} not found or metadata unavailable",
                        "details": e.message,
                        "request_id": request_id,
                    },
                )
            except ExportError as e:
                # Convert export errors to server errors
                log.error(f"Export error for job {job_id}: {e.message}")
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error": e.error_type,
                        "message": e.message,
                        "suggestions": e.recovery_suggestions,
                        "request_id": request_id,
                    },
                )
            except HTTPException:
                # Re-raise HTTP exceptions as-is (don't wrap them)
                raise
            except Exception as e:
                # Handle unexpected errors
                log.error(
                    f"Unexpected error during export orchestration for job {job_id}: {str(e)}"
                )
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error": "internal_server_error",
                        "message": "An unexpected error occurred during export processing",
                        "request_id": request_id,
                    },
                )

    async def _analyze_metadata(self, job_id: int, request_id: str) -> JobMetadata:
        """
        Analyze job metadata to understand dataset characteristics.

        This method retrieves comprehensive metadata about the job including
        row counts, available periods, facts list, and axes information.
        This information is used to determine optimal export strategies.

        Args:
            job_id: Job ID to analyze
            request_id: Request ID for error tracking

        Returns:
            JobMetadata containing comprehensive job information

        Raises:
            MetadataRetrievalError: If metadata cannot be retrieved
            ExportError: For other analysis errors
        """
        try:
            log.debug(f"Analyzing metadata for job {job_id}")

            # Use the metadata analyzer to retrieve job metadata
            metadata = await self.metadata_analyzer.analyze_job_metadata(
                job_id=job_id, request_id=request_id
            )

            log.debug(
                f"Metadata analysis completed for job {job_id}: "
                f"{metadata.total_rows:,} rows, {len(metadata.available_periods)} periods, "
                f"{len(metadata.facts_list)} facts"
            )

            return metadata

        except (MetadataRetrievalError, ExportError):
            # Re-raise metadata and export errors as-is
            raise
        except Exception as e:
            raise ExportError(
                error_type="metadata_analysis_error",
                message=f"Failed to analyze metadata for job {job_id}: {str(e)}",
                context={
                    "job_id": job_id,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
                request_id=request_id,
                recovery_suggestions=[
                    "Verify the job ID is correct",
                    "Check database connectivity",
                    "Retry the export request",
                ],
            ) from e

    async def _select_optimization_strategy(
        self,
        metadata: JobMetadata,
        format: ExportFormat,
        options: ExportOptions,
        request_id: str,
    ) -> OptimizationStrategy:
        """
        Select optimization strategy based on metadata and export requirements.

        This method analyzes the dataset characteristics and export requirements
        to determine the optimal processing strategy including chunking approach,
        memory limits, and temporary file management.

        Args:
            metadata: Job metadata containing dataset characteristics
            format: Target export format
            options: Export options
            request_id: Request ID for error tracking

        Returns:
            OptimizationStrategy with recommended processing approach

        Raises:
            ExcelLimitExceededError: If Excel limits would be exceeded
            ExportError: For other strategy selection errors
        """
        try:
            log.debug(f"Selecting optimization strategy for job {metadata.job_id}")

            # Validate Excel limits if Excel format is requested
            if format == ExportFormat.EXCEL:
                await self.metadata_analyzer.validate_excel_limits(
                    metadata=metadata, options=options, request_id=request_id
                )

            # Calculate size estimates for the target format
            size_estimate = (
                await self.metadata_analyzer.calculate_export_size_estimates(
                    metadata=metadata, format=format, request_id=request_id
                )
            )

            # Determine chunking strategy based on dataset size
            strategy = await self.metadata_analyzer.determine_chunking_strategy(
                metadata=metadata, request_id=request_id
            )

            log.debug(
                f"Optimization strategy selected for job {metadata.job_id}: "
                f"streaming={strategy.use_streaming}, chunk_size={strategy.chunk_size:,}, "
                f"period_chunking={strategy.period_chunking}, memory_limit={strategy.memory_limit_mb}MB"
            )

            return strategy

        except (ExcelLimitExceededError, ExportError):
            # Re-raise specific export errors as-is
            raise
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            raise ExportError(
                error_type="strategy_selection_error",
                message=f"Failed to select optimization strategy for job {metadata.job_id}: {str(e)}",
                context={
                    "job_id": metadata.job_id,
                    "format": format.value,
                    "total_rows": metadata.total_rows,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
                request_id=request_id,
                recovery_suggestions=[
                    "Try a different export format",
                    "Enable period separation to reduce dataset size",
                    "Contact support if the issue persists",
                ],
            ) from e

    async def _execute_export(
        self,
        metadata: JobMetadata,
        strategy: OptimizationStrategy,
        format: ExportFormat,
        options: ExportOptions,
        request_id: str,
        job_info: Optional[Any] = None,
        return_download_url: bool = False,
        request: Optional[Any] = None,
    ) -> Union[StreamingResponse, Dict[str, Any]]:
        """
        Execute export using the selected optimization strategy.

        This method implements strategy-based processing with support for
        large dataset handling through period-based chunking and streaming
        response generation. It coordinates with the format converter to
        generate the final export file.

        Args:
            metadata: Job metadata containing dataset information
            strategy: Optimization strategy for processing
            format: Target export format
            options: Export options
            request_id: Request ID for error tracking
            job_info: Optional job information for Excel info sheet
            return_download_url: Whether to return download URL instead of streaming response
            request: Optional FastAPI request object for URL generation

        Returns:
            StreamingResponse containing the exported data, or Dict with download URL

        Raises:
            ExportError: If export execution fails
        """
        try:
            log.debug(
                f"Executing export for job {metadata.job_id} using {strategy.temp_file_strategy.value} strategy"
            )

            # Generate temporary file path for export processing using temp_file_manager
            temp_file_id = f"export_{request_id}_{uuid.uuid4().hex[:8]}"

            # Map format to proper file extension
            extension_map = {
                ExportFormat.CSV: "csv",
                ExportFormat.EXCEL: "xlsx",
                ExportFormat.PARQUET: "parquet",
            }
            file_extension = extension_map.get(format, "tmp")

            temp_file_path = temp_file_manager.get_temp_path(
                file_id=temp_file_id,
                name=f"job_{metadata.job_id}",
                extension=file_extension,
                category=FileCategory.EXPORT,
            )

            # Log the mapping for debugging
            log.debug(
                f"Created temp file mapping: {temp_file_id} -> {temp_file_path.name}"
            )

            # Generate the appropriate query based on export options
            query = await self._generate_export_query(metadata, options, strategy)

            # Execute format conversion based on the target format
            if format == ExportFormat.CSV:
                output_path = await self.format_converter.convert_to_csv_streaming(
                    query=query,
                    options=options,
                    temp_file_path=str(temp_file_path),
                    request_id=request_id,
                    job_metadata=metadata,
                )
                content_type = "application/zip" if options.compression else "text/csv"
                filename = f"job_{metadata.job_id}_export.{'zip' if options.compression else 'csv'}"

                # Ensure the file mapping is stored correctly after successful creation
                if output_path and Path(output_path).exists():
                    actual_filename = Path(output_path).name
                    log.debug(
                        f"Ensuring file mapping: {temp_file_id} -> {actual_filename}"
                    )
                    temp_file_manager._store_file_mapping(temp_file_id, actual_filename)

            elif format == ExportFormat.EXCEL:
                # Use job_info from JobMetadata if not provided explicitly
                effective_job_info = (
                    job_info if job_info is not None else metadata.job_info
                )
                log.info(
                    f"Excel export job_info details: "
                    f"provided_job_info={job_info is not None}, "
                    f"metadata_job_info={metadata.job_info is not None}, "
                    f"effective_job_info={effective_job_info is not None}, "
                    f"type={type(effective_job_info)}"
                )
                if effective_job_info and isinstance(effective_job_info, dict):
                    log.info(f"Job info contains {len(effective_job_info)} fields")
                else:
                    log.warning(f"Job info is empty or invalid: {effective_job_info}")

                output_path = await self.format_converter.convert_to_excel_streaming(
                    query=query,
                    options=options,
                    temp_file_path=str(temp_file_path),
                    request_id=request_id,
                    job_metadata=metadata,
                    job_info=effective_job_info,
                )
                # Set content type and filename based on period separation mode
                if (
                    options.separate_periods
                    and options.separate_periods_mode == PeriodSeparation.FILES
                ):
                    content_type = "application/zip"
                    filename = f"job_{metadata.job_id}_export_periods.zip"
                else:
                    content_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    filename = f"job_{metadata.job_id}_export.xlsx"

                # Ensure the file mapping is stored correctly after successful creation
                if output_path and Path(output_path).exists():
                    actual_filename = Path(output_path).name
                    log.debug(
                        f"Ensuring file mapping: {temp_file_id} -> {actual_filename}"
                    )
                    temp_file_manager._store_file_mapping(temp_file_id, actual_filename)

            elif format == ExportFormat.PARQUET:
                output_path = await self.format_converter.convert_to_parquet_streaming(
                    query=query,
                    options=options,
                    temp_file_path=str(temp_file_path),
                    request_id=request_id,
                    job_metadata=metadata,
                )
                content_type = "application/octet-stream"
                filename = f"job_{metadata.job_id}_export.parquet"

                # Ensure the file mapping is stored correctly after successful creation
                if output_path and Path(output_path).exists():
                    actual_filename = Path(output_path).name
                    log.debug(
                        f"Ensuring file mapping: {temp_file_id} -> {actual_filename}"
                    )
                    temp_file_manager._store_file_mapping(temp_file_id, actual_filename)

            else:
                raise ExportError(
                    error_type="unsupported_format",
                    message=f"Unsupported export format: {format.value}",
                    context={
                        "job_id": metadata.job_id,
                        "format": format.value,
                        "supported_formats": [f.value for f in ExportFormat],
                    },
                    request_id=request_id,
                    recovery_suggestions=[
                        "Use a supported export format (CSV, Excel, Parquet)",
                        "Check the format parameter in your request",
                    ],
                )

            # Create streaming response from the generated file
            response = await self._create_streaming_response(
                file_path=output_path,
                content_type=content_type,
                filename=filename,
                request_id=request_id,
                temp_file_id=temp_file_id,
                return_download_url=return_download_url,
                request=request,
            )

            log.debug(
                f"Export execution completed for job {metadata.job_id}, file: {output_path}"
            )
            return response

        except ExportError:
            # Re-raise export errors as-is
            raise
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            raise ExportError(
                error_type="export_execution_error",
                message=f"Failed to execute export for job {metadata.job_id}: {str(e)}",
                context={
                    "job_id": metadata.job_id,
                    "format": format.value,
                    "strategy": strategy.temp_file_strategy.value,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
                request_id=request_id,
                recovery_suggestions=[
                    "Retry the export request",
                    "Try a different export format",
                    "Enable period separation for large datasets",
                    "Contact support if the issue persists",
                ],
            ) from e

    async def _generate_export_query(
        self,
        metadata: JobMetadata,
        options: ExportOptions,
        strategy: OptimizationStrategy,
    ) -> str:
        """
        Generate the appropriate SQL query for export based on options and strategy.

        This method creates optimized SQL queries based on the export options
        (layout, period separation) and optimization strategy (chunking approach).
        It handles both vertical and horizontal layouts with appropriate query
        generation for each case.

        Args:
            metadata: Job metadata containing table and structure information
            options: Export options including layout preferences
            strategy: Optimization strategy for query generation

        Returns:
            SQL query string optimized for the export requirements
        """
        # Import here to avoid circular imports
        from magic_gateway.export.data.streaming_retriever import StreamingDataRetriever

        # Create streaming data retriever for query generation
        retriever = StreamingDataRetriever(self.connection_manager)

        # Construct full table name (avoid duplicating schema if already present)
        if "." in metadata.table_name:
            # Table name already includes schema/database prefix
            table_name = metadata.table_name
        else:
            # Add database prefix to table name
            table_name = f"{metadata.database_name}.{metadata.table_name}"

        # Generate query based on layout option
        if options.horizontal_facts and metadata.facts_list:
            # Extract axes information from job_info with proper type checking
            log.debug(
                f"job_info type: {type(metadata.job_info)}, value: {metadata.job_info}"
            )
            axes_info = {}
            if metadata.job_info and isinstance(metadata.job_info, dict):
                axes_info = metadata.job_info.get("axes", {})

            # Use pivot query for horizontal facts layout
            # Note: period_filter will be handled during streaming if period chunking is enabled
            query = await retriever.get_pivot_query(
                job_id=metadata.job_id,
                facts_list=metadata.facts_list,
                table_name=table_name,
                axes_info=axes_info,
                period_filter=None,  # Will be set during period chunking if needed
            )
        else:
            # Use standard query for vertical layout with proper type checking
            log.debug(
                f"job_info type: {type(metadata.job_info)}, value: {metadata.job_info}"
            )
            axes_info = {}
            if metadata.job_info and isinstance(metadata.job_info, dict):
                axes_info = metadata.job_info.get("axes", {})

            query = await retriever.get_streaming_query(
                job_id=metadata.job_id,
                table_name=table_name,
                layout=options.excel_layout,
                axes_info=axes_info,
            )

        return query

    async def _create_streaming_response(
        self,
        file_path: str,
        content_type: str,
        filename: str,
        request_id: str,
        temp_file_id: Optional[str] = None,
        return_download_url: bool = False,
        request: Optional[Any] = None,
    ) -> Union[StreamingResponse, Dict[str, Any]]:
        """
        Create a streaming response from the exported file.

        This method creates a FastAPI StreamingResponse that streams the
        exported file content to the client while handling proper cleanup
        of temporary files after the response is sent.

        Args:
            file_path: Path to the exported file
            content_type: MIME content type for the response
            filename: Filename for the Content-Disposition header
            request_id: Request ID for error tracking
            temp_file_id: Optional temporary file ID for cleanup
            return_download_url: Whether to return download URL instead of streaming response
            request: Optional FastAPI request object for URL generation

        Returns:
            StreamingResponse configured for file download, or Dict with download URL

        Raises:
            ExportError: If response creation fails
        """
        try:
            import os
            from pathlib import Path

            # Verify the file exists and is readable
            if not os.path.exists(file_path):
                raise ExportError(
                    error_type="export_file_not_found",
                    message=f"Export file not found: {file_path}",
                    context={"file_path": file_path, "filename": filename},
                    request_id=request_id,
                    recovery_suggestions=[
                        "Retry the export request",
                        "Check temporary file storage availability",
                    ],
                )

            # Get file size for Content-Length header
            file_size = os.path.getsize(file_path)

            # If download URL is requested (e.g., for Swagger UI), return URL instead of streaming
            if return_download_url:
                # Generate a secure download URL that bypasses OpenAPI documentation
                download_url = await self._generate_download_url(
                    temp_file_id, filename, file_size, request
                )

                log.info(
                    f"Generated download URL for file {filename} ({file_size:,} bytes) - "
                    f"file preserved for download via URL"
                )

                return {
                    "download_url": download_url,
                    "file_id": temp_file_id,
                    "filename": filename,
                    "file_size": file_size,
                    "content_type": content_type,
                    "message": "File ready for download. Use the download_url or file_id for download endpoint to retrieve the file.",
                    "instructions": "Copy the download_url and paste it in a new browser tab to download the file directly.",
                }

            async def file_streamer():
                """Generator function to stream file content with memory monitoring."""
                from magic_gateway.utils.parquet_processor import get_memory_usage_mb
                import asyncio

                streaming_completed = False
                bytes_streamed = 0
                initial_memory_mb = get_memory_usage_mb()

                # Calculate adaptive chunk size based on file size and available memory
                chunk_size = self._calculate_streaming_chunk_size(
                    file_size, initial_memory_mb
                )

                try:
                    with open(file_path, "rb") as file:
                        chunk_count = 0
                        while True:
                            chunk = file.read(chunk_size)
                            if not chunk:
                                break

                            bytes_streamed += len(chunk)
                            chunk_count += 1
                            yield chunk

                            # Monitor memory usage periodically for large files
                            from magic_gateway.core.config import settings

                            if (
                                chunk_count
                                % settings.STREAMING_MEMORY_MONITORING_INTERVAL
                                == 0
                                and file_size > 100 * 1024 * 1024
                            ):  # Use configured monitoring interval for files > 100MB
                                current_memory_mb = get_memory_usage_mb()
                                memory_increase_mb = (
                                    current_memory_mb - initial_memory_mb
                                )

                                # Log memory usage for large files
                                log.debug(
                                    f"File streaming progress: {bytes_streamed / (1024 * 1024):.1f}MB / {file_size / (1024 * 1024):.1f}MB "
                                    f"({100 * bytes_streamed / file_size:.1f}%), "
                                    f"Memory: {current_memory_mb:.1f}MB (+{memory_increase_mb:.1f}MB)"
                                )

                                # Adjust chunk size if memory pressure is detected
                                if (
                                    memory_increase_mb
                                    > settings.STREAMING_MEMORY_PRESSURE_THRESHOLD_MB
                                ):
                                    new_chunk_size = max(
                                        settings.STREAMING_MIN_CHUNK_SIZE,
                                        chunk_size // 2,
                                    )  # Reduce chunk size
                                    if new_chunk_size != chunk_size:
                                        chunk_size = new_chunk_size
                                        log.warning(
                                            f"Reducing streaming chunk size to {chunk_size} bytes due to memory pressure "
                                            f"(memory increase: {memory_increase_mb:.1f}MB, threshold: {settings.STREAMING_MEMORY_PRESSURE_THRESHOLD_MB}MB)"
                                        )

                            # Yield control to allow other tasks to run
                            await asyncio.sleep(0)

                    # Mark streaming as completed only if we reach this point without exceptions
                    streaming_completed = True
                    log.debug(
                        f"File streaming completed successfully: {file_path} ({bytes_streamed:,} bytes)"
                    )

                except Exception as streaming_error:
                    log.error(
                        f"File streaming failed for {file_path} after {bytes_streamed:,} bytes: {streaming_error}"
                    )
                    # Don't mark as completed on streaming errors
                    raise
                finally:
                    # Only clean up and mark as downloaded if streaming completed successfully
                    if streaming_completed:
                        if temp_file_id:
                            try:
                                temp_file_manager.cleanup_file(temp_file_id)
                                log.debug(
                                    f"Cleaned up temporary export file after successful streaming: {file_path}"
                                )
                            except Exception as cleanup_error:
                                log.warning(
                                    f"Failed to clean up temporary file {file_path}: {cleanup_error}"
                                )
                        else:
                            # Log warning but don't attempt direct file deletion
                            log.warning(
                                f"Cannot clean up file - temp_file_id not available for {file_path}"
                            )
                    else:
                        # Streaming failed - preserve the file for potential retry
                        log.warning(
                            f"Preserving temporary file due to streaming failure: {file_path}"
                        )
                        if temp_file_id:
                            log.info(
                                f"File {temp_file_id} preserved for potential retry (not marked as downloaded)"
                            )

            # Create streaming response with appropriate headers
            headers = {
                "Content-Disposition": f'attachment; filename="{filename}"',
                "Content-Length": str(file_size),
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0",
            }

            response = StreamingResponse(
                file_streamer(), media_type=content_type, headers=headers
            )

            log.debug(
                f"Created streaming response for file {filename} ({file_size:,} bytes)"
            )
            return response

        except ExportError:
            # Re-raise export errors as-is
            raise
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            raise ExportError(
                error_type="streaming_response_error",
                message=f"Failed to create streaming response: {str(e)}",
                context={
                    "file_path": file_path,
                    "filename": filename,
                    "content_type": content_type,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
                request_id=request_id,
                recovery_suggestions=[
                    "Retry the export request",
                    "Check file system permissions",
                    "Verify temporary file storage availability",
                ],
            ) from e

    async def _generate_download_url(
        self,
        temp_file_id: str,
        filename: str,
        file_size: int,
        request_id: str,
        request: Optional[Any] = None,
    ) -> str:
        """
        Generate a secure download URL using the scripts/download endpoint for Swagger UI compatibility.

        This method creates a download URL that uses the existing scripts/download endpoint,
        which is designed to handle file downloads with proper cleanup and is compatible
        with Swagger UI usage.

        Args:
            temp_file_id: Temporary file ID for tracking
            filename: Original filename for the download
            file_size: Size of the file in bytes
            request: Optional FastAPI request object to extract base URL

        Returns:
            Complete download URL with protocol and host using scripts/download endpoint
        """
        # Create the relative download URL path using scripts/download endpoint
        relative_url = f"/api/v1/scripts/download/{temp_file_id}"

        # Try to construct full URL with protocol and host
        if request:
            try:
                # Extract base URL from the request
                base_url = f"{request.url.scheme}://{request.url.netloc}"
                download_url = f"{base_url}{relative_url}"
            except Exception as e:
                log.error(f"Failed to extract base URL from request: {e}")
                # Raise error instead of falling back to relative URL
                raise ExportError(
                    message="Failed to generate download URL from request",
                    error_type="url_generation_failed",
                    request_id=request_id,
                    context={
                        "error": str(e),
                        "has_request": request is not None,
                    },
                    recovery_suggestions=[
                        "Check that the request object contains valid URL information",
                        "Verify server configuration for URL generation",
                        "Contact support if the issue persists",
                    ],
                )
        else:
            # If no request provided, try to get from settings or use relative URL
            try:
                from magic_gateway.core.config import settings

                # Use configured base URL settings
                protocol = getattr(settings, "BASE_URL_PROTOCOL", "http")
                host = getattr(settings, "BASE_URL_HOST", None) or getattr(
                    settings, "HOST", "localhost"
                )
                port = getattr(settings, "BASE_URL_PORT", None) or getattr(
                    settings, "PORT", 8000
                )

                # Construct URL based on standard port conventions
                if (port == 80 and protocol == "http") or (
                    port == 443 and protocol == "https"
                ):
                    download_url = f"{protocol}://{host}{relative_url}"
                else:
                    download_url = f"{protocol}://{host}:{port}{relative_url}"
            except Exception as e:
                log.error(f"Failed to construct full URL from settings: {e}")
                # Raise error instead of falling back to relative URL
                raise ExportError(
                    message="Failed to generate download URL from settings",
                    error_type="url_generation_failed",
                    request_id=request_id,
                    context={
                        "error": str(e),
                        "relative_url": relative_url,
                    },
                    recovery_suggestions=[
                        "Check BASE_URL_PROTOCOL and BASE_URL_HOST settings",
                        "Verify server configuration for URL generation",
                        "Ensure all required URL settings are properly configured",
                        "Contact support if the issue persists",
                    ],
                )

        log.debug(
            f"Generated scripts/download URL for file {filename} ({file_size:,} bytes): {download_url}"
        )

        return download_url

    def _calculate_streaming_chunk_size(
        self, file_size_bytes: int, available_memory_mb: float
    ) -> int:
        """
        Calculate optimal chunk size for file streaming based on file size and available memory.

        This method determines the best chunk size for streaming large files to minimize
        memory usage while maintaining reasonable performance using configured settings.

        Args:
            file_size_bytes: Size of the file to be streamed in bytes
            available_memory_mb: Available memory in MB

        Returns:
            Optimal chunk size in bytes for streaming
        """
        from magic_gateway.core.config import settings

        # Base chunk sizes for different file size categories (from configuration)
        if file_size_bytes > 1024 * 1024 * 1024:  # > 1GB files
            base_chunk_size = settings.STREAMING_CHUNK_SIZE_HUGE_FILES
        elif file_size_bytes > 100 * 1024 * 1024:  # > 100MB files
            base_chunk_size = settings.STREAMING_CHUNK_SIZE_LARGE_FILES
        elif file_size_bytes > 10 * 1024 * 1024:  # > 10MB files
            base_chunk_size = settings.STREAMING_CHUNK_SIZE_MEDIUM_FILES
        else:
            base_chunk_size = settings.STREAMING_CHUNK_SIZE_SMALL_FILES

        # Adjust based on available memory (use configured percentage of available memory per chunk)
        memory_based_chunk = int(
            available_memory_mb
            * 1024
            * 1024
            * settings.STREAMING_MEMORY_PERCENTAGE_LIMIT
        )

        # Use the smaller of the two to be conservative
        optimal_chunk_size = min(base_chunk_size, memory_based_chunk)

        # Ensure minimum and maximum chunk sizes from configuration
        optimal_chunk_size = max(settings.STREAMING_MIN_CHUNK_SIZE, optimal_chunk_size)
        optimal_chunk_size = min(settings.STREAMING_MAX_CHUNK_SIZE, optimal_chunk_size)

        log.debug(
            f"Calculated streaming chunk size: {optimal_chunk_size} bytes "
            f"(file: {file_size_bytes / (1024 * 1024):.1f}MB, memory: {available_memory_mb:.1f}MB)"
        )

        return optimal_chunk_size
